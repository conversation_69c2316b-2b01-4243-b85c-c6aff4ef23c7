.contact img {
  max-width: 100%;
}

.contact .form__message {
  align-items: flex-start;
}

.contact .icon-success {
  margin-top: 0.2rem;
}

.contact .field {
  margin-bottom: 1.5rem;
}
.contact-desc p{font-family: Abadi MT;
    font-style: normal;
    font-weight: 400;
    font-size: 1.3vw;
    line-height: 26px;
    color: #fff;
    text-align: center;
    margin: 2vw 2vw 4vw;}

.contact-desc p a {
    color: #54ece9;
    text-decoration: none;
}
@media screen and (min-width: 750px) {
  .contact .field {
    margin-bottom: 2rem;
  }
}

.contact__button {
  margin-top: 3rem;
}

@media screen and (min-width: 750px) {
  .contact__button {
    margin-top: 4rem;
  }
}

@media screen and (min-width: 750px) {
  .contact__fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 2rem;
  }
}
 @media screen and (max-width: 767px) {
    .contact-desc p {
        font-size: 4vw;
        margin-bottom: 7vw;
    }
}